{"MaterialExpressionMaps": [{"ClassCE": "MaterialExpressionAdd", "ClassesUE": ["MaterialExpressionAdd"]}, {"ClassCE": "MaterialExpressionAbs", "ClassesUE": ["MaterialExpressionAbs"]}, {"ClassCE": "MaterialExpressionAppendVector", "ClassesUE": ["MaterialExpressionAppendVector"]}, {"ClassCE": "MaterialExpressionArccosine", "ClassesUE": ["MaterialExpressionArccosine"]}, {"ClassCE": "MaterialExpressionArcsine", "ClassesUE": ["MaterialExpressionArcsine"]}, {"ClassCE": "MaterialExpressionArctangent", "ClassesUE": ["MaterialExpressionArctangent"]}, {"ClassCE": "MaterialExpressionArctangent2", "ClassesUE": ["MaterialExpressionArctangent2"]}, {"ClassCE": "MaterialExpressionBumpOffset", "ClassesUE": ["MaterialExpressionBumpOffset"]}, {"ClassCE": "MaterialExpressionCast", "ClassesUE": ["MaterialExpressionCast"]}, {"ClassCE": "MaterialExpressionCameraPositionWS", "ClassesUE": ["MaterialExpressionCameraPositionWS"]}, {"ClassCE": "MaterialExpressionCameraPositionWSCoordinateConvertExtension", "ClassesUE": ["MaterialExpressionCameraPositionWSCoordinateConvertExtension"], "Composition": ["MaterialExpressionCameraPositionWS", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input"}}, {"ClassCE": "MaterialExpressionCameraTilePosition", "ClassesUE": ["MaterialExpressionCameraTilePosition"]}, {"ClassCE": "MaterialExpressionCameraVectorWS", "ClassesUE": ["MaterialExpressionCameraVectorWS"]}, {"ClassCE": "MaterialExpressionCeil", "ClassesUE": ["MaterialExpressionCeil"]}, {"ClassCE": "MaterialExpressionPanner", "ClassesUE": ["MaterialExpressionPanner"]}, {"ClassCE": "MaterialExpressionClamp", "ClassesUE": ["MaterialExpressionClamp"], "KeyMap": {"Input": "0:Value"}}, {"ClassCE": "MaterialExpressionComment", "ClassesUE": ["MaterialExpressionComment"]}, {"ClassCE": "MaterialExpressionConstant", "ClassesUE": ["MaterialExpressionConstant"], "KeyMap": {"R": "Const"}}, {"ClassCE": "MaterialExpressionConstant2Vector", "ClassesUE": ["MaterialExpressionConstant2Vector"], "KeyMap": {"R": "X", "G": "Y"}}, {"ClassCE": "MaterialExpressionConstant3Vector", "ClassesUE": ["MaterialExpressionConstant3Vector"], "KeyMap": {"Constant": "Value"}}, {"ClassCE": "MaterialExpressionConstant4Vector", "ClassesUE": ["MaterialExpressionConstant4Vector"], "KeyMap": {"Constant": "Value"}}, {"ClassCE": "MaterialExpressionCosine", "ClassesUE": ["MaterialExpressionCosine"]}, {"ClassCE": "MaterialExpressionCrossProduct", "ClassesUE": ["MaterialExpressionCrossProduct"]}, {"ClassCE": "MaterialExpressionComponentMask", "ClassesUE": ["MaterialExpressionComponentMask"]}, {"ClassCE": "MaterialExpressionCustom", "ClassesUE": ["MaterialExpressionCustom"]}, {"ClassCE": "MaterialExpressionCustomInterpolator", "ClassesUE": ["MaterialExpressionCustomInterpolator"]}, {"ClassCE": "MaterialExpressionDDX", "ClassesUE": ["MaterialExpressionDDX"], "KeyMap": {"Value": "Input"}}, {"ClassCE": "MaterialExpressionDDY", "ClassesUE": ["MaterialExpressionDDY"], "KeyMap": {"Value": "Input"}}, {"ClassCE": "MaterialExpressionDepthFade", "ClassesUE": ["MaterialExpressionDepthFade"]}, {"ClassCE": "MaterialExpressionDivide", "ClassesUE": ["MaterialExpressionDivide"]}, {"ClassCE": "MaterialExpressionDotProduct", "ClassesUE": ["MaterialExpressionDotProduct"]}, {"ClassCE": "MaterialExpressionExponential", "ClassesUE": ["MaterialExpressionExponential"]}, {"ClassCE": "MaterialExpressionExponential2", "ClassesUE": ["MaterialExpressionExponential2"]}, {"ClassCE": "MaterialExpressionFloor", "ClassesUE": ["MaterialExpressionFloor"]}, {"ClassCE": "MaterialExpressionFmod", "ClassesUE": ["MaterialExpressionFmod"]}, {"ClassCE": "MaterialExpressionFrac", "ClassesUE": ["MaterialExpressionFrac"]}, {"ClassCE": "MaterialExpressionFunctionCall", "ClassesUE": ["MaterialExpressionMaterialFunctionCall"]}, {"ClassCE": "MaterialExpressionFunctionInput", "ClassesUE": ["MaterialExpressionFunctionInput"]}, {"ClassCE": "MaterialExpressionFunctionOutput", "ClassesUE": ["MaterialExpressionFunctionOutput"]}, {"ClassCE": "MaterialExpressionGPUSceneDataBool", "ClassesUE": ["MaterialExpressionGPUSceneDataBool"]}, {"ClassCE": "MaterialExpressionGPUSceneDataFloat1", "ClassesUE": ["MaterialExpressionGPUSceneDataFloat1"]}, {"ClassCE": "MaterialExpressionGPUSceneDataFloat2", "ClassesUE": ["MaterialExpressionGPUSceneDataFloat2"]}, {"ClassCE": "MaterialExpressionGPUSceneDataFloat3", "ClassesUE": ["MaterialExpressionGPUSceneDataFloat3"]}, {"ClassCE": "MaterialExpressionGPUSceneDataFloat4", "ClassesUE": ["MaterialExpressionGPUSceneDataFloat4"]}, {"ClassCE": "MaterialExpressionGPUSceneDataUInt1", "ClassesUE": ["MaterialExpressionGPUSceneDataUInt1"]}, {"ClassCE": "MaterialExpressionGPUSceneDataUInt2", "ClassesUE": ["MaterialExpressionGPUSceneDataUInt2"]}, {"ClassCE": "MaterialExpressionGPUSceneDataUInt3", "ClassesUE": ["MaterialExpressionGPUSceneDataUInt3"]}, {"ClassCE": "MaterialExpressionGPUSceneDataUInt4", "ClassesUE": ["MaterialExpressionGPUSceneDataUInt4"]}, {"ClassCE": "MaterialExpressionIf", "ClassesUE": ["MaterialExpressionIf"]}, {"ClassCE": "MaterialExpressionLerp", "ClassesUE": ["MaterialExpressionLerp", "MaterialExpressionLinearInterpolate"]}, {"ClassCE": "MaterialExpressionLogarithm2", "ClassesUE": ["MaterialExpressionLogarithm2"]}, {"ClassCE": "MaterialExpressionLogarithm10", "ClassesUE": ["MaterialExpressionLogarithm10"]}, {"ClassCE": "MaterialExpressionLuminance", "ClassesUE": ["MaterialExpressionMaterialXLuminance"]}, {"ClassCE": "MaterialExpressionMaterialParameterCollection", "ClassesUE": ["MaterialExpressionCollectionParameter"]}, {"ClassCE": "MaterialExpressionMax", "ClassesUE": ["MaterialExpressionMax"]}, {"ClassCE": "MaterialExpressionMin", "ClassesUE": ["MaterialExpressionMin"]}, {"ClassCE": "MaterialExpressionMultiply", "ClassesUE": ["MaterialExpressionMultiply"]}, {"ClassCE": "MaterialExpressionNormalize", "ClassesUE": ["MaterialExpressionNormalize"], "KeyMap": {"VectorInput": "0:Input"}}, {"ClassCE": "MaterialExpressionOneMinus", "ClassesUE": ["MaterialExpressionOneMinus"]}, {"ClassCE": "MaterialExpressionParticleAnimatedVelocity", "ClassesUE": ["MaterialExpressionParticleAnimatedVelocity"]}, {"ClassCE": "MaterialExpressionParticleColor", "ClassesUE": ["MaterialExpressionParticleColor"]}, {"ClassCE": "MaterialExpressionParticlePosition", "ClassesUE": ["MaterialExpressionParticlePosition"]}, {"ClassCE": "MaterialExpressionParticleRotation", "ClassesUE": ["MaterialExpressionParticleRotation"]}, {"ClassCE": "MaterialExpressionParticleSizeScale", "ClassesUE": ["MaterialExpressionParticleSizeScale"]}, {"ClassCE": "MaterialExpressionParticleUVScale", "ClassesUE": ["MaterialExpressionParticleUVScale"]}, {"ClassCE": "MaterialExpressionPixelDepth", "ClassesUE": ["MaterialExpressionPixelDepth"]}, {"ClassCE": "MaterialExpressionPower", "ClassesUE": ["MaterialExpressionPower"]}, {"ClassCE": "MaterialExpressionRotateAboutAxis", "ClassesUE": ["MaterialExpressionRotateAboutAxis"]}, {"ClassCE": "MaterialExpressionRound", "ClassesUE": ["MaterialExpressionRound"]}, {"ClassCE": "MaterialExpressionSaturate", "ClassesUE": ["MaterialExpressionSaturate"], "KeyMap": {"Input": "Value"}}, {"ClassCE": "MaterialExpressionScalarParameter", "ClassesUE": ["MaterialExpressionScalarParameter"]}, {"ClassCE": "MaterialExpressionSceneTexture", "ClassesUE": ["MaterialExpressionSceneTexture"]}, {"ClassCE": "MaterialExpressionScreenUV", "ClassesUE": ["MaterialExpressionScreenUV"]}, {"ClassCE": "MaterialExpressionShaderConstFloat", "ClassesUE": ["MaterialExpressionShaderConstFloat"]}, {"ClassCE": "MaterialExpressionSign", "ClassesUE": ["MaterialExpressionSign"]}, {"ClassCE": "MaterialExpressionSine", "ClassesUE": ["MaterialExpressionSine"]}, {"ClassCE": "MaterialExpressionSkyAtmosphereLightDirection", "ClassesUE": ["MaterialExpressionSkyAtmosphereLightDirection"]}, {"ClassCE": "MaterialExpressionSkyAtmosphereLightDirectionCoordinateConvertExtension", "ClassesUE": ["MaterialExpressionSkyAtmosphereLightDirectionCoordinateConvertExtension"], "Composition": ["MaterialExpressionSkyAtmosphereLightDirection", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input"}}, {"ClassCE": "MaterialExpressionSkyAtmosphereLightDiskLuminance", "ClassesUE": ["MaterialExpressionSkyAtmosphereLightDiskLuminance"]}, {"ClassCE": "MaterialExpressionSkyAtmosphereLightIlluminanceOnGround", "ClassesUE": ["MaterialExpressionSkyAtmosphereLightIlluminanceOnGround"]}, {"ClassCE": "MaterialExpressionSkyAtmosphereViewLuminance", "ClassesUE": ["MaterialExpressionSkyAtmosphereViewLuminance"]}, {"ClassCE": "MaterialExpressionSquareRoot", "ClassesUE": ["MaterialExpressionSquareRoot"]}, {"ClassCE": "MaterialExpressionStaticBool", "ClassesUE": ["MaterialExpressionStaticBool"]}, {"ClassCE": "MaterialExpressionShaderConstBool", "ClassesUE": ["MaterialExpressionStaticBoolParameter"]}, {"ClassCE": "MaterialExpressionStaticSwitch", "ClassesUE": ["MaterialExpressionStaticSwitch"]}, {"ClassCE": "MaterialExpressionStaticSwitchParameter", "ClassesUE": ["MaterialExpressionStaticSwitchParameter"], "Composition": ["MaterialExpressionShaderConstBool", "MaterialExpressionStaticSwitch"], "KeyMap": {"0:0": "1:Value", "True": "1:True", "False": "1:<PERSON><PERSON><PERSON>"}}, {"ClassCE": "MaterialExpressionStep", "ClassesUE": ["MaterialExpressionStep"]}, {"ClassCE": "MaterialExpressionSubtract", "ClassesUE": ["MaterialExpressionSubtract"]}, {"ClassCE": "MaterialExpressionSurfaceShader", "ClassesUE": ["MaterialExpressionSurfaceShader"]}, {"ClassCE": "MaterialExpressionTangent", "ClassesUE": ["MaterialExpressionTangent"]}, {"ClassCE": "MaterialExpressionTextureCoordinate", "ClassesUE": ["MaterialExpressionTextureCoordinate"]}, {"ClassCE": "MaterialExpressionTextureObject", "ClassesUE": ["MaterialExpressionTextureObject"], "KeyMap": {"TextureObject": "0:TextureString"}}, {"ClassCE": "MaterialExpressionTextureSample", "ClassesUE": ["MaterialExpressionTextureSample"], "KeyMap": {"TextureObject": "0:<PERSON>", "Coordinates": "0:UV", "MipLevel": "0:Level", "MipBias": "0:<PERSON><PERSON>", "DDX(UVs)": "0:DDX", "DDY(UVs)": "0:DDY"}}, {"ClassCE": "MaterialExpressionTextureParameter", "ClassesUE": ["MaterialExpressionTextureObjectParameter"], "KeyMap": {"TextureObject": "0:TextureString", "Coordinates": "0:UV"}}, {"ClassCE": "MaterialExpressionDistance", "ClassesUE": ["MaterialExpressionDistance"]}, {"ClassCE": "MaterialExpressionPixelNormalWS", "ClassesUE": ["MaterialExpressionPixelNormalWS"]}, {"ClassCE": "MaterialExpressionPixelNormalWSCoordinateConvertExtension", "ClassesUE": ["MaterialExpressionPixelNormalWSCoordinateConvertExtension"], "Composition": ["MaterialExpressionPixelNormalWS", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input"}}, {"ClassCE": "MaterialExpressionFunctionInputTexturePreview", "ClassesUE": ["MaterialExpressionFunctionInputTexturePreview"], "Composition": ["MaterialExpressionTextureObject", "MaterialExpressionFunctionInput"], "KeyMap": {"0:0": "1:Preview"}, "ExtraData": [{"SubId": 0, "Datas": {"m_TextureString": ""}}]}, {"ClassCE": "MaterialExpressionTextureSampleParameter", "ClassesUE": ["MaterialExpressionTextureSampleParameter2D", "MaterialExpressionTextureSampleParameter"], "KeyMap": {"TextureObject": "0:<PERSON>", "Coordinates": "0:UV", "MipLevel": "0:Level", "MipBias": "0:<PERSON><PERSON>", "DDX(UVs)": "0:DDX", "DDY(UVs)": "0:DDY"}}, {"ClassCE": "MaterialExpressionTerrainColor", "ClassesUE": ["MaterialExpressionTerrainColor"]}, {"ClassCE": "MaterialExpressionTerrainLayerCoords", "ClassesUE": ["MaterialExpressionLandscapeLayerCoords"]}, {"ClassCE": "MaterialExpressionTerrainLayerWeight", "ClassesUE": ["MaterialExpressionLandscapeLayerWeight"]}, {"ClassCE": "MaterialExpressionTerrainLayerBlend", "ClassesUE": ["MaterialExpressionLandscapeLayerBlend"]}, {"ClassCE": "MaterialExpressionTime", "ClassesUE": ["MaterialExpressionTime"]}, {"ClassCE": "MaterialExpressionObjectPositionWS", "ClassesUE": ["MaterialExpressionObjectPositionWS"]}, {"ClassCE": "MaterialExpressionObjectPositionWSCoordinateConvertExtension", "ClassesUE": ["MaterialExpressionObjectPositionWSCoordinateConvertExtension"], "Composition": ["MaterialExpressionObjectPositionWS", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input"}}, {"ClassCE": "MaterialExpressionVertexColor", "ClassesUE": ["MaterialExpressionVertexColor"]}, {"ClassCE": "MaterialExpressionTransform", "ClassesUE": ["MaterialExpressionTransform", "MaterialExpressionTransformPosition"]}, {"ClassCE": "MaterialExpressionTransformCoordinateConvertExtension", "ClassesUE": ["MaterialExpressionTransformCoordinateConvertExtension", "MaterialExpressionTransformPositionCoordinateConvertExtension"], "Composition": ["MaterialExpressionCEToUEWorldPosition", "MaterialExpressionTransform", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input", "1:0": "2:Input"}, "ExtraData": [{"SubId": 0, "Datas": {"m_CEToUE": false}}]}, {"ClassCE": "MaterialExpressionTransformTangentToWSExtension", "ClassesUE": ["MaterialExpressionTransformTangentToWSExtension"], "Composition": ["MaterialExpressionTransform", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input"}}, {"ClassCE": "MaterialExpressionTransformWSToTangentExtension", "ClassesUE": ["MaterialExpressionTransformWSToTangentExtension"], "Composition": ["MaterialExpressionCEToUEWorldPosition", "MaterialExpressionTransform", "MaterialExpressionComponentMask"], "KeyMap": {"0:0": "1:Input", "1:0": "2:Input"}, "ExtraData": [{"SubId": 0, "Datas": {"m_CEToUE": false}}, {"SubId": 2, "Datas": {"m_R": true, "m_G": true, "m_B": true, "m_A": false}}]}, {"ClassCE": "MaterialExpressionTruncate", "ClassesUE": ["MaterialExpressionTruncate"]}, {"ClassCE": "MaterialExpressionConstant", "ClassesUE": ["MaterialExpressionTwoSidedSign"], "ExtraData": [{"SubId": 0, "Datas": {"m_Const": 1.0}}]}, {"ClassCE": "MaterialExpressionVectorParameterComposition", "ClassesUE": ["MaterialExpressionVectorParameter"], "Composition": ["MaterialExpressionVectorParameter", "MaterialExpressionComponentMask"], "KeyMap": {"0:0": "1:Input"}, "OutputMap": [{"SubId": 1, "OutputIndex": 0}, {"SubId": 0, "OutputIndex": 1}, {"SubId": 0, "OutputIndex": 2}, {"SubId": 0, "OutputIndex": 3}, {"SubId": 0, "OutputIndex": 4}], "ExtraData": [{"SubId": 1, "Datas": {"m_R": true, "m_G": true, "m_B": true, "m_A": false}}]}, {"ClassCE": "MaterialExpressionVertexID", "ClassesUE": ["MaterialExpressionVertexID"]}, {"ClassCE": "MaterialExpressionVertexShader", "ClassesUE": ["MaterialExpressionVertexShader"]}, {"ClassCE": "MaterialExpressionWorldGeometryNormal", "ClassesUE": ["MaterialExpressionVertexNormalWS"]}, {"ClassCE": "MaterialExpressionWorldGeometryNormalCoordinateConvertExtension", "ClassesUE": ["MaterialExpressionVertexNormalWSCoordinateConvertExtension"], "Composition": ["MaterialExpressionWorldGeometryNormal", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input"}}, {"ClassCE": "MaterialExpressionWorldPosition", "ClassesUE": ["MaterialExpressionWorldPosition"]}, {"ClassCE": "MaterialExpressionWorldPositionCoordinateConvertExtension", "ClassesUE": ["MaterialExpressionWorldPositionCoordinateConvertExtension"], "Composition": ["MaterialExpressionWorldPosition", "MaterialExpressionCEToUEWorldPosition"], "KeyMap": {"0:0": "1:Input"}}, {"ClassCE": "MaterialExpressionWorldTangent", "ClassesUE": ["MaterialExpressionWorldTangent"]}, {"ClassCE": "MaterialExpressionRerouter", "ClassesUE": ["MaterialExpressionReroute"]}, {"ClassCE": "MaterialExpressionRerouter", "ClassesUE": ["MaterialExpressionFeatureLevelSwitch", "MaterialExpressionQualitySwitch", "MaterialExpressionVertexInterpolator"], "KeyMap": {"Default": "Input", "VS": "Input"}}, {"ClassCE": "MaterialExpressionSobol", "ClassesUE": ["MaterialExpressionSobol"]}, {"ClassCE": "MaterialExpressionDesaturation", "ClassesUE": ["MaterialExpressionDesaturation"]}, {"ClassCE": "MaterialExpressionMakeMaterialAttributes", "ClassesUE": ["MaterialExpressionMakeMaterialAttributes"]}, {"ClassCE": "MaterialExpressionBreakMaterialAttributes", "ClassesUE": ["MaterialExpressionBreakMaterialAttributes"], "KeyMap": {"Attr": "MaterialAttributes"}}, {"ClassCE": "MaterialExpressionGetMaterialAttributes", "ClassesUE": ["MaterialExpressionGetMaterialAttributes"], "KeyMap": {"None": "MaterialAttributesInput", "BaseColor": "1", "Metallic": "2", "Specular": "3", "Roughness": "4", "Opacity": "5", "OpacityMask": "6", "Normal": "7", "AmbientOcclusion": "8", "EmissiveColor": "9", "SubsurfaceColor": "10", "WorldPositionOffset": "11", "ShadingModel": "12", "Anisotropy": "13", "Tangent": "14", "ClearCoat": "15", "ClearCoatRoughness": "16", "Refraction": "17", "PixelDepthOffset": "18", "Displacement": "19"}}, {"ClassCE": "MaterialExpressionSetMaterialAttributes", "ClassesUE": ["MaterialExpressionSetMaterialAttributes"], "KeyMap": {"MaterialAttributes": "MaterialAttributes", "BaseColor": "1", "Metallic": "2", "Specular": "3", "Roughness": "4", "Opacity": "5", "OpacityMask": "6", "Normal": "7", "AmbientOcclusion": "8", "EmissiveColor": "9", "SubsurfaceColor": "10", "WorldPositionOffset": "11", "ShadingModel": "12", "Anisotropy": "13", "Tangent": "14", "ClearCoat": "15", "ClearCoatRoughness": "16", "Refraction": "17", "PixelDepthOffset": "18", "Displacement": "19"}}, {"ClassCE": "MaterialExpressionBlendMaterialAttributes", "ClassesUE": ["MaterialExpressionBlendMaterialAttributes"]}, {"ClassCE": "MaterialExpressionNamedRerouteDeclaration", "ClassesUE": ["MaterialExpressionNamedRerouteDeclaration"]}, {"ClassCE": "MaterialExpressionNamedRerouteUsage", "ClassesUE": ["MaterialExpressionNamedRerouteUsage"]}, {"ClassCE": "MaterialExpressionConstantBiasScale", "ClassesUE": ["MaterialExpressionConstantBiasScale"]}, {"ClassCE": "MaterialExpressionSphereMask", "ClassesUE": ["MaterialExpressionSphereMask"]}, {"ClassCE": "MaterialExpressionShadowReplace", "ClassesUE": ["MaterialExpressionShadowReplace"]}, {"ClassCE": "MaterialExpressionStaticSwitch", "ClassesUE": ["MaterialExpressionDistanceFieldsRenderingSwitch"], "KeyMap": {"Yes": "True", "No": "False"}, "ExtraData": [{"DefaultValue": false}]}, {"ClassCE": "MaterialExpressionFresnel", "ClassesUE": ["MaterialExpressionFresnel"]}, {"ClassCE": "MaterialExpressionScreenPosition", "ClassesUE": ["MaterialExpressionScreenPosition"]}], "MaterialFunctionMaps": [{"UEMaterialFunctionPath": "Contents/Engine/Functions/Engine_MaterialFunctions02/ObjectLocalBounds.materialfunction", "CEMaterialFunctionPath": "EngineResource/MaterialFunction/ObjectLocalBounds.materialfunction"}]}